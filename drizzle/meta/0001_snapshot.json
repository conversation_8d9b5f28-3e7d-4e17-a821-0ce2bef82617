{"id": "afc9053a-a8c3-413e-be19-cccf44cb2fc1", "prevId": "362e8a25-6901-4ee6-a185-1e2d0e70e771", "version": "7", "dialect": "postgresql", "tables": {"public.achievements": {"name": "achievements", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "icon_name": {"name": "icon_name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "requirement": {"name": "requirement", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.food_suggestions": {"name": "food_suggestions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "rating_id": {"name": "rating_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"food_suggestions_user_id_users_id_fk": {"name": "food_suggestions_user_id_users_id_fk", "tableFrom": "food_suggestions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ratings": {"name": "ratings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "food_suggestion_id": {"name": "food_suggestion_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "dislikes": {"name": "dislikes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ratings_food_suggestion_id_food_suggestions_id_fk": {"name": "ratings_food_suggestion_id_food_suggestions_id_fk", "tableFrom": "ratings", "tableTo": "food_suggestions", "columnsFrom": ["food_suggestion_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ratings_user_id_users_id_fk": {"name": "ratings_user_id_users_id_fk", "tableFrom": "ratings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_achievements": {"name": "user_achievements", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "achievement_id": {"name": "achievement_id", "type": "uuid", "primaryKey": false, "notNull": true}, "unlocked_at": {"name": "unlocked_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "progress": {"name": "progress", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {"user_achievements_user_id_users_id_fk": {"name": "user_achievements_user_id_users_id_fk", "tableFrom": "user_achievements", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_achievements_achievement_id_achievements_id_fk": {"name": "user_achievements_achievement_id_achievements_id_fk", "tableFrom": "user_achievements", "tableTo": "achievements", "columnsFrom": ["achievement_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_compatibility": {"name": "user_compatibility", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id_1": {"name": "user_id_1", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id_2": {"name": "user_id_2", "type": "uuid", "primaryKey": false, "notNull": true}, "compatibility_score": {"name": "compatibility_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "shared_votes": {"name": "shared_votes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_comparisons": {"name": "total_comparisons", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_calculated": {"name": "last_calculated", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_compatibility_user_id_1_users_id_fk": {"name": "user_compatibility_user_id_1_users_id_fk", "tableFrom": "user_compatibility", "tableTo": "users", "columnsFrom": ["user_id_1"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_compatibility_user_id_2_users_id_fk": {"name": "user_compatibility_user_id_2_users_id_fk", "tableFrom": "user_compatibility", "tableTo": "users", "columnsFrom": ["user_id_2"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_profiles": {"name": "user_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "department": {"name": "department", "type": "text", "primaryKey": false, "notNull": false}, "joined_year": {"name": "joined_year", "type": "integer", "primaryKey": false, "notNull": false}, "dietary_preferences": {"name": "dietary_preferences", "type": "text", "primaryKey": false, "notNull": false}, "favorite_restaurants": {"name": "favorite_restaurants", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_profiles_user_id_users_id_fk": {"name": "user_profiles_user_id_users_id_fk", "tableFrom": "user_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_profiles_user_id_unique": {"name": "user_profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_stats": {"name": "user_stats", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "total_votes": {"name": "total_votes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_suggestions": {"name": "total_suggestions", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_wins": {"name": "total_wins", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_top_picks": {"name": "total_top_picks", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "average_rating": {"name": "average_rating", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false, "default": "'0.00'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_stats_user_id_users_id_fk": {"name": "user_stats_user_id_users_id_fk", "tableFrom": "user_stats", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_stats_user_id_unique": {"name": "user_stats_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.voting_history": {"name": "voting_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "food_suggestion_id": {"name": "food_suggestion_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vote_type": {"name": "vote_type", "type": "text", "primaryKey": false, "notNull": true}, "voted_at": {"name": "voted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"voting_history_user_id_users_id_fk": {"name": "voting_history_user_id_users_id_fk", "tableFrom": "voting_history", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "voting_history_food_suggestion_id_food_suggestions_id_fk": {"name": "voting_history_food_suggestion_id_food_suggestions_id_fk", "tableFrom": "voting_history", "tableTo": "food_suggestions", "columnsFrom": ["food_suggestion_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}
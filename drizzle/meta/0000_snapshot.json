{"id": "362e8a25-6901-4ee6-a185-1e2d0e70e771", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.food_suggestions": {"name": "food_suggestions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "rating_id": {"name": "rating_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ratings": {"name": "ratings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "food_suggestion_id": {"name": "food_suggestion_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "dislikes": {"name": "dislikes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ratings_food_suggestion_id_food_suggestions_id_fk": {"name": "ratings_food_suggestion_id_food_suggestions_id_fk", "tableFrom": "ratings", "tableTo": "food_suggestions", "columnsFrom": ["food_suggestion_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}
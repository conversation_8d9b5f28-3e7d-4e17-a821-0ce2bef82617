/**
 * Simple test script to verify API endpoints work correctly
 * Run with: npx tsx scripts/test-api.ts
 */

const BASE_URL = 'http://localhost:3001';

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

async function testAPI() {
  console.log('🧪 Testing API endpoints...\n');

  try {
    // Test 1: Get food suggestions
    console.log('1. Testing GET /api/food-suggestions');
    const suggestionsResponse = await fetch(`${BASE_URL}/api/food-suggestions`);
    const suggestionsResult: ApiResponse<any[]> = await suggestionsResponse.json();
    
    if (suggestionsResult.success) {
      console.log(`✅ Found ${suggestionsResult.data?.length || 0} food suggestions`);
    } else {
      console.log(`❌ Error: ${suggestionsResult.error}`);
    }

    // Test 2: Get user data
    console.log('\n2. Testing GET /api/users/[id]');
    const userResponse = await fetch(`${BASE_URL}/api/users/64c3ab49-7c5d-4f14-b06e-1c5c8e9993ed`);
    const userResult: ApiResponse<any> = await userResponse.json();
    
    if (userResult.success) {
      console.log(`✅ Found user: ${userResult.data?.displayName}`);
    } else {
      console.log(`❌ Error: ${userResult.error}`);
    }

    // Test 3: Get user stats
    console.log('\n3. Testing GET /api/users/[id]/stats');
    const statsResponse = await fetch(`${BASE_URL}/api/users/64c3ab49-7c5d-4f14-b06e-1c5c8e9993ed/stats`);
    const statsResult: ApiResponse<any> = await statsResponse.json();
    
    if (statsResult.success) {
      console.log(`✅ User stats - Votes: ${statsResult.data?.totalVotes}, Suggestions: ${statsResult.data?.totalSuggestions}`);
    } else {
      console.log(`❌ Error: ${statsResult.error}`);
    }

    // Test 4: Get user achievements
    console.log('\n4. Testing GET /api/users/[id]/achievements');
    const achievementsResponse = await fetch(`${BASE_URL}/api/users/64c3ab49-7c5d-4f14-b06e-1c5c8e9993ed/achievements`);
    const achievementsResult: ApiResponse<any[]> = await achievementsResponse.json();
    
    if (achievementsResult.success) {
      const unlocked = achievementsResult.data?.filter(a => a.isUnlocked).length || 0;
      const total = achievementsResult.data?.length || 0;
      console.log(`✅ Achievements: ${unlocked}/${total} unlocked`);
    } else {
      console.log(`❌ Error: ${achievementsResult.error}`);
    }

    // Test 5: Get user compatibility
    console.log('\n5. Testing GET /api/users/[id]/compatibility');
    const compatibilityResponse = await fetch(`${BASE_URL}/api/users/64c3ab49-7c5d-4f14-b06e-1c5c8e9993ed/compatibility`);
    const compatibilityResult: ApiResponse<any[]> = await compatibilityResponse.json();
    
    if (compatibilityResponse.ok && compatibilityResult.success) {
      console.log(`✅ Found ${compatibilityResult.data?.length || 0} compatibility records`);
    } else {
      console.log(`❌ Error: ${compatibilityResult.error}`);
    }

    // Test 6: Get voting history
    console.log('\n6. Testing GET /api/users/[id]/voting-history');
    const historyResponse = await fetch(`${BASE_URL}/api/users/64c3ab49-7c5d-4f14-b06e-1c5c8e9993ed/voting-history?limit=5`);
    const historyResult: ApiResponse<any[]> = await historyResponse.json();
    
    if (historyResult.success) {
      console.log(`✅ Found ${historyResult.data?.length || 0} voting history records`);
    } else {
      console.log(`❌ Error: ${historyResult.error}`);
    }

    console.log('\n🎉 API testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('\n💡 Make sure the development server is running with: npm run dev');
  }
}

// Run the test
testAPI();

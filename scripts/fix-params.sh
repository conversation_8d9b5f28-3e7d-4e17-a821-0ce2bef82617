#!/bin/bash

# Script to fix Next.js 15 params awaiting in API routes

echo "Fixing API route params..."

# Fix food suggestions routes
sed -i 's/{ params }: { params: { id: string } }/{ params }: { params: Promise<{ id: string }> }/g' app/api/food-suggestions/[id]/route.ts
sed -i 's/{ params }: { params: { id: string } }/{ params }: { params: Promise<{ id: string }> }/g' app/api/food-suggestions/[id]/vote/route.ts

# Fix user stats route
sed -i 's/{ params }: { params: { id: string } }/{ params }: { params: Promise<{ id: string }> }/g' app/api/users/[id]/stats/route.ts

# Fix user achievements routes
sed -i 's/{ params }: { params: { id: string } }/{ params }: { params: Promise<{ id: string }> }/g' app/api/users/[id]/achievements/route.ts
sed -i 's/{ params }: { params: { id: string; achievementId: string } }/{ params }: { params: Promise<{ id: string; achievementId: string }> }/g' app/api/users/[id]/achievements/[achievementId]/route.ts

# Fix user compatibility route
sed -i 's/{ params }: { params: { id: string } }/{ params }: { params: Promise<{ id: string }> }/g' app/api/users/[id]/compatibility/route.ts

# Fix voting history route
sed -i 's/{ params }: { params: { id: string } }/{ params }: { params: Promise<{ id: string }> }/g' app/api/users/[id]/voting-history/route.ts

echo "Fixed API route type signatures. Now manually add await params statements..."

import * as fs from 'fs';
import * as path from 'path';

// <PERSON>ript to fix all Next.js 15 params awaiting issues

const files = [
  'app/api/food-suggestions/[id]/route.ts',
  'app/api/food-suggestions/[id]/vote/route.ts',
  'app/api/users/[id]/achievements/route.ts',
  'app/api/users/[id]/achievements/[achievementId]/route.ts',
  'app/api/users/[id]/compatibility/route.ts',
  'app/api/users/[id]/voting-history/route.ts',
];

function fixFile(filePath: string) {
  console.log(`Fixing ${filePath}...`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Fix type signatures
  content = content.replace(
    /{ params }: { params: { id: string } }/g,
    '{ params }: { params: Promise<{ id: string }> }'
  );
  
  content = content.replace(
    /{ params }: { params: { id: string; achievementId: string } }/g,
    '{ params }: { params: Promise<{ id: string; achievementId: string }> }'
  );
  
  // Add await params at the beginning of functions
  content = content.replace(
    /(export async function \w+\(\s*request: NextRequest,\s*{ params }: { params: Promise<[^}]+> }\s*\) {\s*try {\s*)/g,
    '$1const { id } = await params;\n    '
  );
  
  content = content.replace(
    /(export async function \w+\(\s*request: NextRequest,\s*{ params }: { params: Promise<{ id: string; achievementId: string }> }\s*\) {\s*try {\s*)/g,
    '$1const { id, achievementId } = await params;\n    '
  );
  
  // Replace params.id with id
  content = content.replace(/params\.id/g, 'id');
  content = content.replace(/params\.achievementId/g, 'achievementId');
  
  fs.writeFileSync(filePath, content);
  console.log(`Fixed ${filePath}`);
}

files.forEach(fixFile);
console.log('All files fixed!');

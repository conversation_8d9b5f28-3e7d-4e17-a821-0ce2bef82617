# Meal Match - Food Suggestion & Voting App

A Next.js application for team meal suggestions and voting with user profiles, achievements, and compatibility tracking.

## Features

### Food Suggestions
- ✅ Create, read, update, delete food suggestions
- ✅ Vote on suggestions (like/dislike)
- ✅ Real-time vote counting
- ✅ Image support for suggestions

### User Management
- ✅ User profiles with bio, location, department
- ✅ Dietary preferences and favorite restaurants
- ✅ User statistics (votes, suggestions, wins)
- ✅ Achievement system with progress tracking
- ✅ User compatibility scoring
- ✅ Voting history tracking

### Database
- ✅ Neon Postgres integration
- ✅ Drizzle ORM with type safety
- ✅ Multiple related models for comprehensive user data
- ✅ Database migrations and seeding

## Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: Neon Postgres with Drizzle ORM
- **UI Components**: Custom components with Lucide icons

## Database Schema

### Core Tables
- `users` - Basic user information
- `user_profiles` - Extended profile data
- `user_stats` - Voting and suggestion statistics
- `food_suggestions` - Meal suggestions
- `ratings` - Vote data for suggestions

### Feature Tables
- `achievements` - Available achievements
- `user_achievements` - User achievement progress
- `user_compatibility` - Compatibility scores between users
- `voting_history` - Detailed voting patterns

## Setup Instructions

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Setup
Create a `.env.local` file with your Neon database URL:
```env
DATABASE_URL="****************************************************************"
```

### 3. Database Setup
```bash
# Generate migration files
npm run db:generate

# Push schema to database (for development)
npm run db:push

# Or run migrations (for production)
npm run db:migrate

# Seed the database with sample data
npm run db:seed
```

### 4. Development
```bash
npm run dev
```

### 5. Test API Endpoints
```bash
npx tsx scripts/test-api.ts
```

## API Endpoints

### Food Suggestions
- `GET /api/food-suggestions` - List all suggestions
- `POST /api/food-suggestions` - Create suggestion
- `GET /api/food-suggestions/[id]` - Get specific suggestion
- `PUT /api/food-suggestions/[id]` - Update suggestion
- `DELETE /api/food-suggestions/[id]` - Delete suggestion
- `POST /api/food-suggestions/[id]/vote` - Vote on suggestion

### Users
- `GET /api/users/[id]` - Get user with profile and stats
- `PUT /api/users/[id]` - Update user basic info
- `GET /api/users/[id]/profile` - Get user profile
- `PUT /api/users/[id]/profile` - Update user profile
- `GET /api/users/[id]/stats` - Get user statistics
- `GET /api/users/[id]/achievements` - Get user achievements
- `POST /api/users/[id]/achievements` - Check/unlock achievements
- `GET /api/users/[id]/compatibility` - Get compatibility data
- `POST /api/users/[id]/compatibility/calculate` - Calculate compatibility
- `GET /api/users/[id]/voting-history` - Get voting history

## Key Features Implemented

### CRUD Operations
- Complete CRUD for food suggestions
- Complete CRUD for user profiles and data
- Proper error handling and validation
- Type-safe API responses

### User System
- Multi-model user architecture
- Achievement system with progress tracking
- Compatibility scoring between users
- Comprehensive voting history
- Real-time statistics updates

### Database Integration
- Neon Postgres with Drizzle ORM
- Foreign key relationships
- Database transactions for data consistency
- Automatic stats updates on user actions

### Frontend Features
- Edit/delete functionality for suggestions
- Profile editing modal
- Real-time data fetching
- Loading states and error handling
- Achievement progress visualization

## Development Notes

- Uses hardcoded user ID "user-1" for demo purposes
- All API endpoints include proper error handling
- Database operations use transactions where needed
- User stats are automatically updated on votes/suggestions
- Achievement progress is calculated in real-time

## Next Steps

- Add authentication system
- Implement real-time notifications
- Add more achievement types
- Enhance compatibility algorithm
- Add admin panel for managing achievements

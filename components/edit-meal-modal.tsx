"use client"

import { useState, useEffect } from "react"
import { ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, DialogContent, DialogHeader } from "@/components/ui/dialog"
import type { ApiResponse, FoodSuggestion } from "@/lib/types"

interface EditMealModalProps {
  isOpen: boolean
  onClose: () => void
  suggestion: FoodSuggestion | null
  onSuggestionUpdated?: (suggestion: FoodSuggestion) => void
}

export function EditMealModal({ isOpen, onClose, suggestion, onSuggestionUpdated }: EditMealModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    imageUrl: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Update form data when suggestion changes
  useEffect(() => {
    if (suggestion) {
      setFormData({
        name: suggestion.name,
        description: suggestion.description || "",
        imageUrl: suggestion.imageUrl || "",
      })
    }
  }, [suggestion])

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = async () => {
    if (!suggestion || !formData.name.trim()) {
      setError("Name is required")
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch(`/api/food-suggestions/${suggestion.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          imageUrl: formData.imageUrl || null,
          description: formData.description || null,
        }),
      })

      const result: ApiResponse<FoodSuggestion> = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to update suggestion')
      }

      // Call the callback to notify parent component
      if (onSuggestionUpdated && result.data) {
        onSuggestionUpdated(result.data)
      }

      onClose()
    } catch (error) {
      console.error('Error updating meal suggestion:', error)
      setError(error instanceof Error ? error.message : 'Failed to update suggestion')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePreviewPicture = () => {
    if (formData.imageUrl) {
      window.open(formData.imageUrl, "_blank")
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-zinc-900 border-zinc-700 text-white p-0 max-w-md mx-auto h-full max-h-screen overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <DialogHeader className="flex flex-row items-center justify-between p-4 pt-12 border-b border-zinc-800">
            <Button variant="ghost" size="icon" className="text-white hover:bg-zinc-800" onClick={onClose}>
              <ArrowLeft className="h-6 w-6" />
            </Button>
            <h2 className="text-xl font-semibold">Edit Meal</h2>
            <div className="w-10" />
          </DialogHeader>

          {/* Form Content */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            <div className="space-y-4">
              <Input
                placeholder="Food Name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 h-12"
              />

              <Textarea
                placeholder="Description"
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 min-h-[120px] resize-none"
              />

              <div className="space-y-3">
                <Input
                  placeholder="Food Picture URL"
                  value={formData.imageUrl}
                  onChange={(e) => handleInputChange("imageUrl", e.target.value)}
                  className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 h-12"
                />

                <Button
                  variant="outline"
                  onClick={handlePreviewPicture}
                  disabled={!formData.imageUrl}
                  className="w-full bg-zinc-800 border-zinc-700 text-white hover:bg-zinc-700"
                >
                  Preview Picture
                </Button>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-zinc-800 space-y-3">
            {error && (
              <div className="text-red-400 text-sm text-center">
                {error}
              </div>
            )}
            <Button
              onClick={handleSubmit}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white h-12 rounded-full"
              disabled={!formData.name.trim() || isSubmitting}
            >
              {isSubmitting ? "Updating..." : "Update"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

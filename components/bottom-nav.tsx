import Link from "next/link"
import { Home, CheckCircle, RotateCcw, User, Medal } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface BottomNavProps {
  currentPage: "home" | "vote" | "history" | "profile" | "achievements"
}

export function BottomNav({ currentPage }: BottomNavProps) {
  const navItems = [
    { id: "home", label: "Home", icon: Home, href: "/" },
    { id: "vote", label: "Vote", icon: CheckCircle, href: "/vote" },
    { id: "achievements", label: "Awards", icon: Medal, href: "/achievements" },
    { id: "history", label: "History", icon: RotateCcw, href: "/history" },
    { id: "profile", label: "Profile", icon: User, href: "/profile" },
  ]

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-zinc-900 border-t border-zinc-800">
      <div className="flex items-center justify-around py-3">
        {navItems.map((item) => {
          const IconComponent = item.icon
          const isActive = currentPage === item.id
          return (
            <Link key={item.id} href={item.href}>
              <Button
                variant="ghost"
                className={`flex flex-col items-center gap-1 hover:bg-zinc-800 ${
                  isActive ? "text-white" : "text-zinc-400"
                }`}
              >
                <IconComponent className="h-5 w-5" />
                <span className="text-xs">{item.label}</span>
              </Button>
            </Link>
          )
        })}
      </div>
    </div>
  )
}

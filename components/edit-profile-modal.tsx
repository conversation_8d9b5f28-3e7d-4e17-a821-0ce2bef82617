"use client"

import { useState, useEffect } from "react"
import { ArrowLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, DialogContent, DialogHeader } from "@/components/ui/dialog"
import type { ApiResponse, UserWithProfile, UserProfileFormData } from "@/lib/types"

interface EditProfileModalProps {
  isOpen: boolean
  onClose: () => void
  user: UserWithProfile | null
  onProfileUpdated?: (user: UserWithProfile) => void
}

export function EditProfileModal({ isOpen, onClose, user, onProfileUpdated }: EditProfileModalProps) {
  const [formData, setFormData] = useState<UserProfileFormData>({
    displayName: "",
    bio: "",
    location: "",
    department: "",
    dietaryPreferences: [],
    favoriteRestaurants: [],
    avatarUrl: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Update form data when user changes
  useEffect(() => {
    if (user) {
      setFormData({
        displayName: user.displayName,
        bio: user.profile?.bio || "",
        location: user.profile?.location || "",
        department: user.profile?.department || "",
        dietaryPreferences: user.profile?.dietaryPreferences || [],
        favoriteRestaurants: user.profile?.favoriteRestaurants || [],
        avatarUrl: user.avatarUrl || "",
      })
    }
  }, [user])

  const handleInputChange = (field: keyof UserProfileFormData, value: string | string[]) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleArrayInputChange = (field: 'dietaryPreferences' | 'favoriteRestaurants', value: string) => {
    const items = value.split(',').map(item => item.trim()).filter(item => item.length > 0)
    setFormData((prev) => ({
      ...prev,
      [field]: items,
    }))
  }

  const handleSubmit = async () => {
    if (!user || !formData.displayName.trim()) {
      setError("Display name is required")
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      // Update basic user info
      const userResponse = await fetch(`/api/users/${user.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          displayName: formData.displayName,
          avatarUrl: formData.avatarUrl,
        }),
      })

      const userResult: ApiResponse<any> = await userResponse.json()

      if (!userResult.success) {
        throw new Error(userResult.error || 'Failed to update user')
      }

      // Update profile
      const profileResponse = await fetch(`/api/users/${user.id}/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bio: formData.bio,
          location: formData.location,
          department: formData.department,
          dietaryPreferences: formData.dietaryPreferences,
          favoriteRestaurants: formData.favoriteRestaurants,
        }),
      })

      const profileResult: ApiResponse<any> = await profileResponse.json()

      if (!profileResult.success) {
        throw new Error(profileResult.error || 'Failed to update profile')
      }

      // Create updated user object
      const updatedUser: UserWithProfile = {
        ...user,
        displayName: formData.displayName,
        avatarUrl: formData.avatarUrl || null,
        profile: {
          ...user.profile,
          bio: formData.bio || null,
          location: formData.location || null,
          department: formData.department || null,
          dietaryPreferences: formData.dietaryPreferences,
          favoriteRestaurants: formData.favoriteRestaurants,
        },
      }

      // Call the callback to notify parent component
      if (onProfileUpdated) {
        onProfileUpdated(updatedUser)
      }

      onClose()
    } catch (error) {
      console.error('Error updating profile:', error)
      setError(error instanceof Error ? error.message : 'Failed to update profile')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-zinc-900 border-zinc-700 text-white p-0 max-w-md mx-auto h-full max-h-screen overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <DialogHeader className="flex flex-row items-center justify-between p-4 pt-12 border-b border-zinc-800">
            <Button variant="ghost" size="icon" className="text-white hover:bg-zinc-800" onClick={onClose}>
              <ArrowLeft className="h-6 w-6" />
            </Button>
            <h2 className="text-xl font-semibold">Edit Profile</h2>
            <div className="w-10" />
          </DialogHeader>

          {/* Form Content */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            <div className="space-y-4">
              <Input
                placeholder="Display Name"
                value={formData.displayName}
                onChange={(e) => handleInputChange("displayName", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 h-12"
              />

              <Input
                placeholder="Avatar URL"
                value={formData.avatarUrl}
                onChange={(e) => handleInputChange("avatarUrl", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 h-12"
              />

              <Textarea
                placeholder="Bio"
                value={formData.bio}
                onChange={(e) => handleInputChange("bio", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 min-h-[80px] resize-none"
              />

              <Input
                placeholder="Location"
                value={formData.location}
                onChange={(e) => handleInputChange("location", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 h-12"
              />

              <Input
                placeholder="Department"
                value={formData.department}
                onChange={(e) => handleInputChange("department", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 h-12"
              />

              <Input
                placeholder="Dietary Preferences (comma separated)"
                value={formData.dietaryPreferences?.join(', ') || ''}
                onChange={(e) => handleArrayInputChange("dietaryPreferences", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 h-12"
              />

              <Input
                placeholder="Favorite Restaurants (comma separated)"
                value={formData.favoriteRestaurants?.join(', ') || ''}
                onChange={(e) => handleArrayInputChange("favoriteRestaurants", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 h-12"
              />
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-zinc-800 space-y-3">
            {error && (
              <div className="text-red-400 text-sm text-center">
                {error}
              </div>
            )}
            <Button
              onClick={handleSubmit}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white h-12 rounded-full"
              disabled={!formData.displayName.trim() || isSubmitting}
            >
              {isSubmitting ? "Updating..." : "Update Profile"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

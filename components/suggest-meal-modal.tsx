"use client"

import { useState } from "react"
import { ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>alog, DialogContent, DialogHeader } from "@/components/ui/dialog"
import type { ApiResponse, FoodSuggestion } from "@/lib/types"

interface SuggestMealModalProps {
  isOpen: boolean
  onClose: () => void
  onSuggestionAdded?: (suggestion: FoodSuggestion) => void
}

export function SuggestMealModal({ isOpen, onClose, onSuggestionAdded }: SuggestMealModalProps) {
  const [formData, setFormData] = useState({
    foodName: "",
    restaurant: "",
    dietaryRestrictions: "",
    description: "",
    pictureUrl: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = async () => {
    if (!formData.foodName.trim()) {
      setError("Food name is required")
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      // For now, we'll use a hardcoded userId. In a real app, this would come from authentication
      const userId = "user-1"

      const response = await fetch('/api/food-suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.foodName,
          imageUrl: formData.pictureUrl || null,
          description: formData.description || null,
          userId,
        }),
      })

      const result: ApiResponse<FoodSuggestion> = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to create suggestion')
      }

      // Call the callback to notify parent component
      if (onSuggestionAdded && result.data) {
        onSuggestionAdded(result.data)
      }

      // Reset form and close modal
      setFormData({
        foodName: "",
        restaurant: "",
        dietaryRestrictions: "",
        description: "",
        pictureUrl: "",
      })
      onClose()
    } catch (error) {
      console.error('Error submitting meal suggestion:', error)
      setError(error instanceof Error ? error.message : 'Failed to submit suggestion')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePreviewPicture = () => {
    if (formData.pictureUrl) {
      window.open(formData.pictureUrl, "_blank")
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-zinc-900 border-zinc-700 text-white p-0 max-w-md mx-auto h-full max-h-screen overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <DialogHeader className="flex flex-row items-center justify-between p-4 pt-12 border-b border-zinc-800">
            <Button variant="ghost" size="icon" className="text-white hover:bg-zinc-800" onClick={onClose}>
              <ArrowLeft className="h-6 w-6" />
            </Button>
            <h2 className="text-xl font-semibold">Suggest a Meal</h2>
            <div className="w-10" />
          </DialogHeader>

          {/* Form Content */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            <div className="space-y-4">
              <Input
                placeholder="Food Name"
                value={formData.foodName}
                onChange={(e) => handleInputChange("foodName", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 h-12"
              />

              <Input
                placeholder="Restaurant"
                value={formData.restaurant}
                onChange={(e) => handleInputChange("restaurant", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 h-12"
              />

              <Input
                placeholder="Dietary Restrictions"
                value={formData.dietaryRestrictions}
                onChange={(e) => handleInputChange("dietaryRestrictions", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 h-12"
              />

              <Textarea
                placeholder="Description"
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 min-h-[120px] resize-none"
              />

              <div className="space-y-3">
                <Input
                  placeholder="Food Picture URL"
                  value={formData.pictureUrl}
                  onChange={(e) => handleInputChange("pictureUrl", e.target.value)}
                  className="bg-zinc-800 border-zinc-700 text-white placeholder:text-zinc-400 h-12"
                />

                <Button
                  variant="outline"
                  onClick={handlePreviewPicture}
                  disabled={!formData.pictureUrl}
                  className="w-full bg-zinc-800 border-zinc-700 text-white hover:bg-zinc-700"
                >
                  Preview Picture
                </Button>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-zinc-800 space-y-3">
            {error && (
              <div className="text-red-400 text-sm text-center">
                {error}
              </div>
            )}
            <Button
              onClick={handleSubmit}
              className="w-full bg-pink-500 hover:bg-pink-600 text-white h-12 rounded-full"
              disabled={!formData.foodName.trim() || isSubmitting}
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>

            <button
              className="w-full text-zinc-400 text-sm underline hover:text-zinc-300"
              onClick={() => {
                /* Handle restaurant page suggestion */
              }}
            >
              Or, suggest from a restaurant page
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

import type { ApiResponse } from './types';

/**
 * Enhanced fetch wrapper with better error handling
 */
export async function apiRequest<T>(
  url: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    // Handle non-JSON responses
    const contentType = response.headers.get('content-type');
    if (!contentType?.includes('application/json')) {
      throw new Error(`Invalid response format: ${response.status} ${response.statusText}`);
    }

    const data: ApiResponse<T> = await response.json();

    // Handle API-level errors
    if (!data.success) {
      throw new Error(data.error || `Request failed: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error(`API request failed for ${url}:`, error);
    
    // Return a consistent error format
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

/**
 * Retry wrapper for API requests
 */
export async function apiRequestWithRetry<T>(
  url: string,
  options: RequestInit = {},
  maxRetries: number = 3,
  delay: number = 1000
): Promise<ApiResponse<T>> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await apiRequest<T>(url, options);
      
      if (result.success) {
        return result;
      }
      
      // Don't retry on client errors (4xx)
      if (url.includes('4')) {
        return result;
      }
      
      lastError = new Error(result.error || 'Request failed');
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
    }

    // Wait before retrying (except on last attempt)
    if (attempt < maxRetries) {
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  return {
    success: false,
    error: lastError?.message || 'Request failed after multiple attempts',
  };
}

/**
 * Type-safe API request hooks
 */
export function createApiHook<T>(endpoint: string) {
  return {
    async get(id?: string): Promise<ApiResponse<T>> {
      const url = id ? `${endpoint}/${id}` : endpoint;
      return apiRequest<T>(url);
    },

    async post(data: any): Promise<ApiResponse<T>> {
      return apiRequest<T>(endpoint, {
        method: 'POST',
        body: JSON.stringify(data),
      });
    },

    async put(id: string, data: any): Promise<ApiResponse<T>> {
      return apiRequest<T>(`${endpoint}/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      });
    },

    async delete(id: string): Promise<ApiResponse<{ id: string }>> {
      return apiRequest<{ id: string }>(`${endpoint}/${id}`, {
        method: 'DELETE',
      });
    },
  };
}

/**
 * Common error messages
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  SERVER_ERROR: 'Server error. Please try again later.',
  NOT_FOUND: 'The requested resource was not found.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
} as const;

/**
 * Get user-friendly error message
 */
export function getErrorMessage(error: string | Error): string {
  const message = typeof error === 'string' ? error : error.message;
  
  if (message.includes('fetch')) {
    return ERROR_MESSAGES.NETWORK_ERROR;
  }
  
  if (message.includes('500')) {
    return ERROR_MESSAGES.SERVER_ERROR;
  }
  
  if (message.includes('404')) {
    return ERROR_MESSAGES.NOT_FOUND;
  }
  
  if (message.includes('401') || message.includes('403')) {
    return ERROR_MESSAGES.UNAUTHORIZED;
  }
  
  if (message.includes('400')) {
    return ERROR_MESSAGES.VALIDATION_ERROR;
  }
  
  return message || ERROR_MESSAGES.UNKNOWN_ERROR;
}

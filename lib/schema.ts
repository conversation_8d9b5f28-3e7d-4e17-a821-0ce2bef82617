import { pgTable, text, timestamp, uuid, integer } from 'drizzle-orm/pg-core';

export const foodSuggestions = pgTable('food_suggestions', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  imageUrl: text('image_url'),
  description: text('description'),
  userId: text('user_id').notNull(),
  ratingId: text('rating_id'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const ratings = pgTable('ratings', {
  id: uuid('id').primaryKey().defaultRandom(),
  foodSuggestionId: uuid('food_suggestion_id').references(() => foodSuggestions.id, { onDelete: 'cascade' }).notNull(),
  userId: text('user_id').notNull(),
  likes: integer('likes').default(0).notNull(),
  dislikes: integer('dislikes').default(0).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export type FoodSuggestion = typeof foodSuggestions.$inferSelect;
export type NewFoodSuggestion = typeof foodSuggestions.$inferInsert;
export type Rating = typeof ratings.$inferSelect;
export type NewRating = typeof ratings.$inferInsert;

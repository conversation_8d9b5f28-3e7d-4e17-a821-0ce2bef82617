import { pgTable, text, timestamp, uuid, integer, boolean, decimal } from 'drizzle-orm/pg-core';

// Users table - Basic user information
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  username: text('username').notNull().unique(),
  email: text('email').notNull().unique(),
  displayName: text('display_name').notNull(),
  avatarUrl: text('avatar_url'),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// User profiles table - Extended profile information
export const userProfiles = pgTable('user_profiles', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull().unique(),
  bio: text('bio'),
  location: text('location'),
  department: text('department'),
  joinedYear: integer('joined_year'),
  dietaryPreferences: text('dietary_preferences'), // JSON string for array of preferences
  favoriteRestaurants: text('favorite_restaurants'), // JSON string for array of restaurants
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// User stats table - Voting and suggestion statistics
export const userStats = pgTable('user_stats', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull().unique(),
  totalVotes: integer('total_votes').default(0).notNull(),
  totalSuggestions: integer('total_suggestions').default(0).notNull(),
  totalWins: integer('total_wins').default(0).notNull(), // Suggestions that got the most votes
  totalTopPicks: integer('total_top_picks').default(0).notNull(), // Suggestions in top 3
  averageRating: decimal('average_rating', { precision: 3, scale: 2 }).default('0.00'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const foodSuggestions = pgTable('food_suggestions', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  imageUrl: text('image_url'),
  description: text('description'),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  ratingId: text('rating_id'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const ratings = pgTable('ratings', {
  id: uuid('id').primaryKey().defaultRandom(),
  foodSuggestionId: uuid('food_suggestion_id').references(() => foodSuggestions.id, { onDelete: 'cascade' }).notNull(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  likes: integer('likes').default(0).notNull(),
  dislikes: integer('dislikes').default(0).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Achievements table - Available achievements
export const achievements = pgTable('achievements', {
  id: uuid('id').primaryKey().defaultRandom(),
  title: text('title').notNull(),
  description: text('description').notNull(),
  iconName: text('icon_name').notNull(), // Lucide icon name
  category: text('category').notNull(), // 'voting', 'suggestions', 'social', etc.
  requirement: integer('requirement').notNull(), // Number needed to unlock
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// User achievements table - Track user's earned achievements
export const userAchievements = pgTable('user_achievements', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  achievementId: uuid('achievement_id').references(() => achievements.id, { onDelete: 'cascade' }).notNull(),
  unlockedAt: timestamp('unlocked_at').defaultNow().notNull(),
  progress: integer('progress').default(0).notNull(), // Current progress towards achievement
});

// User compatibility table - Compatibility scores between users
export const userCompatibility = pgTable('user_compatibility', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId1: uuid('user_id_1').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  userId2: uuid('user_id_2').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  compatibilityScore: decimal('compatibility_score', { precision: 5, scale: 2 }).notNull(), // 0.00 to 100.00
  sharedVotes: integer('shared_votes').default(0).notNull(),
  totalComparisons: integer('total_comparisons').default(0).notNull(),
  lastCalculated: timestamp('last_calculated').defaultNow().notNull(),
});

// Voting history table - Track detailed voting patterns
export const votingHistory = pgTable('voting_history', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  foodSuggestionId: uuid('food_suggestion_id').references(() => foodSuggestions.id, { onDelete: 'cascade' }).notNull(),
  voteType: text('vote_type').notNull(), // 'like' or 'dislike'
  votedAt: timestamp('voted_at').defaultNow().notNull(),
});

// Type exports
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type UserProfile = typeof userProfiles.$inferSelect;
export type NewUserProfile = typeof userProfiles.$inferInsert;
export type UserStats = typeof userStats.$inferSelect;
export type NewUserStats = typeof userStats.$inferInsert;
export type Achievement = typeof achievements.$inferSelect;
export type NewAchievement = typeof achievements.$inferInsert;
export type UserAchievement = typeof userAchievements.$inferSelect;
export type NewUserAchievement = typeof userAchievements.$inferInsert;
export type UserCompatibility = typeof userCompatibility.$inferSelect;
export type NewUserCompatibility = typeof userCompatibility.$inferInsert;
export type VotingHistory = typeof votingHistory.$inferSelect;
export type NewVotingHistory = typeof votingHistory.$inferInsert;
export type FoodSuggestion = typeof foodSuggestions.$inferSelect;
export type NewFoodSuggestion = typeof foodSuggestions.$inferInsert;
export type Rating = typeof ratings.$inferSelect;
export type NewRating = typeof ratings.$inferInsert;

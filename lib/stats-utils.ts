import { db } from './db';
import { userStats, votingHistory, foodSuggestions, ratings } from './schema';
import { eq, count, sql } from 'drizzle-orm';

/**
 * Update user statistics after a vote or suggestion action
 */
export async function updateUserStats(userId: string) {
  try {
    // Calculate real-time stats
    const [calculatedStats] = await db
      .select({
        totalVotes: sql<number>`COUNT(DISTINCT ${votingHistory.id})`,
        totalSuggestions: sql<number>`COUNT(DISTINCT ${foodSuggestions.id})`,
      })
      .from(votingHistory)
      .leftJoin(foodSuggestions, eq(votingHistory.userId, foodSuggestions.userId))
      .where(eq(votingHistory.userId, userId));

    // Calculate average rating for user's suggestions
    const [avgRating] = await db
      .select({
        averageRating: sql<string>`COALESCE(AVG(CASE WHEN ${ratings.likes} > 0 THEN 5.0 WHEN ${ratings.dislikes} > 0 THEN 1.0 ELSE 3.0 END), 0.00)`,
      })
      .from(foodSuggestions)
      .leftJoin(ratings, eq(foodSuggestions.id, ratings.foodSuggestionId))
      .where(eq(foodSuggestions.userId, userId));

    // Check if user stats exist
    const [existingStats] = await db
      .select()
      .from(userStats)
      .where(eq(userStats.userId, userId))
      .limit(1);

    const statsData = {
      totalVotes: calculatedStats?.totalVotes || 0,
      totalSuggestions: calculatedStats?.totalSuggestions || 0,
      averageRating: avgRating?.averageRating || '0.00',
      updatedAt: new Date(),
    };

    if (existingStats) {
      // Update existing stats (preserve wins and top picks)
      await db
        .update(userStats)
        .set(statsData)
        .where(eq(userStats.userId, userId));
    } else {
      // Create new stats
      await db
        .insert(userStats)
        .values({
          userId,
          ...statsData,
          totalWins: 0,
          totalTopPicks: 0,
        });
    }

    return true;
  } catch (error) {
    console.error('Error updating user stats:', error);
    return false;
  }
}

/**
 * Check and unlock achievements for a user
 */
export async function checkAndUnlockAchievements(userId: string) {
  try {
    // This will trigger the achievement checking endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/users/${userId}/achievements`, {
      method: 'POST',
    });

    if (response.ok) {
      const result = await response.json();
      return result.data || [];
    }

    return [];
  } catch (error) {
    console.error('Error checking achievements:', error);
    return [];
  }
}

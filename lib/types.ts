// Re-export types from schema for easier imports
export type { FoodSuggestion, NewFoodSuggestion, Rating, NewRating } from './schema';

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Food suggestion with aggregated rating data
export interface FoodSuggestionWithRating extends FoodSuggestion {
  totalLikes: number;
  totalDislikes: number;
  userVote?: 'like' | 'dislike' | null;
}

// Form data for creating/updating food suggestions
export interface FoodSuggestionFormData {
  name: string;
  imageUrl?: string;
  description?: string;
  userId: string;
}

// Vote data
export interface VoteData {
  userId: string;
  voteType: 'like' | 'dislike';
}

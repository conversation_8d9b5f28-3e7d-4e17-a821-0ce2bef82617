// Re-export types from schema for easier imports
export type {
  FoodSuggestion, NewFoodSuggestion, Rating, NewRating,
  User, NewUser, UserProfile, NewUserProfile, UserStats, NewUserStats,
  Achievement, NewAchievement, UserAchievement, NewUserAchievement,
  UserCompatibility, NewUserCompatibility, VotingHistory, NewVotingHistory
} from './schema';

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Food suggestion with aggregated rating data
export interface FoodSuggestionWithRating extends FoodSuggestion {
  totalLikes: number;
  totalDislikes: number;
  userVote?: 'like' | 'dislike' | null;
}

// Form data for creating/updating food suggestions
export interface FoodSuggestionFormData {
  name: string;
  imageUrl?: string;
  description?: string;
  userId: string;
}

// Vote data
export interface VoteData {
  userId: string;
  voteType: 'like' | 'dislike';
}

// User-related interfaces
export interface UserWithProfile extends User {
  profile?: UserProfile;
  stats?: UserStats;
}

export interface UserProfileFormData {
  displayName: string;
  bio?: string;
  location?: string;
  department?: string;
  dietaryPreferences?: string[];
  favoriteRestaurants?: string[];
  avatarUrl?: string;
}

export interface UserStatsData {
  totalVotes: number;
  totalSuggestions: number;
  totalWins: number;
  totalTopPicks: number;
  averageRating: string;
}

export interface AchievementWithProgress extends Achievement {
  isUnlocked: boolean;
  progress: number;
  unlockedAt?: Date;
}

export interface CompatibilityData {
  userId: string;
  displayName: string;
  avatarUrl?: string;
  compatibilityScore: number;
  sharedVotes: number;
  totalComparisons: number;
}

export interface VotingHistoryItem {
  id: string;
  foodSuggestionName: string;
  foodSuggestionImage?: string;
  voteType: 'like' | 'dislike';
  votedAt: Date;
  cuisine?: string;
}

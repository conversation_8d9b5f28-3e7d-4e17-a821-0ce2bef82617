import { db } from './db';
import { foodSuggestions, ratings } from './schema';

async function seed() {
  console.log('🌱 Seeding database...');

  try {
    // Clear existing data
    await db.delete(ratings);
    await db.delete(foodSuggestions);

    // Insert sample food suggestions
    const suggestions = await db.insert(foodSuggestions).values([
      {
        name: 'Tacos',
        description: 'Delicious tacos with a variety of fillings',
        imageUrl: '/placeholder.svg?height=200&width=400',
        userId: 'user-1',
      },
      {
        name: 'Sushi',
        description: 'Fresh sushi rolls and sashimi',
        imageUrl: '/placeholder.svg?height=200&width=400',
        userId: 'user-2',
      },
      {
        name: 'Pizza',
        description: 'Classic pizza with your favorite toppings',
        imageUrl: '/placeholder.svg?height=200&width=400',
        userId: 'user-3',
      },
      {
        name: 'Burger',
        description: 'Juicy beef burger with all the fixings',
        imageUrl: '/placeholder.svg?height=200&width=400',
        userId: 'user-1',
      },
      {
        name: 'Pasta',
        description: 'Creamy pasta with fresh herbs',
        imageUrl: '/placeholder.svg?height=200&width=400',
        userId: 'user-2',
      },
    ]).returning();

    console.log(`✅ Inserted ${suggestions.length} food suggestions`);

    // Insert sample ratings
    const sampleRatings = [];
    for (const suggestion of suggestions) {
      // Add some random votes for each suggestion
      const numVotes = Math.floor(Math.random() * 10) + 1;
      for (let i = 0; i < numVotes; i++) {
        const isLike = Math.random() > 0.3; // 70% chance of like
        sampleRatings.push({
          foodSuggestionId: suggestion.id,
          userId: `user-${Math.floor(Math.random() * 5) + 1}`,
          likes: isLike ? 1 : 0,
          dislikes: isLike ? 0 : 1,
        });
      }
    }

    if (sampleRatings.length > 0) {
      await db.insert(ratings).values(sampleRatings);
      console.log(`✅ Inserted ${sampleRatings.length} ratings`);
    }

    console.log('🎉 Database seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seed function
seed().then(() => {
  console.log('✨ Seeding completed');
  process.exit(0);
});

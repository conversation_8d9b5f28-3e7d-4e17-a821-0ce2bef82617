import { db } from './db';
import {
  users,
  userProfiles,
  userStats,
  achievements,
  userAchievements,
  userCompatibility,
  votingHistory,
  foodSuggestions,
  ratings
} from './schema';

async function seed() {
  console.log('🌱 Seeding database...');

  try {
    // Clear existing data in correct order (due to foreign key constraints)
    await db.delete(userAchievements);
    await db.delete(userCompatibility);
    await db.delete(votingHistory);
    await db.delete(ratings);
    await db.delete(foodSuggestions);
    await db.delete(userStats);
    await db.delete(userProfiles);
    await db.delete(achievements);
    await db.delete(users);

    // Insert sample users
    const sampleUsers = await db.insert(users).values([
      {
        username: 'sophia',
        email: '<EMAIL>',
        displayName: 'Sophia',
        avatarUrl: '/placeholder.svg?height=128&width=128',
      },
      {
        username: 'alex',
        email: '<EMAIL>',
        displayName: '<PERSON>',
        avatarUrl: '/placeholder.svg?height=128&width=128',
      },
      {
        username: 'sarah',
        email: '<EMAIL>',
        displayName: 'Sarah',
        avatarUrl: '/placeholder.svg?height=128&width=128',
      },
      {
        username: 'david',
        email: '<EMAIL>',
        displayName: 'David',
        avatarUrl: '/placeholder.svg?height=128&width=128',
      },
      {
        username: 'ethan',
        email: '<EMAIL>',
        displayName: 'Ethan',
        avatarUrl: '/placeholder.svg?height=128&width=128',
      },
    ]).returning();

    console.log(`✅ Inserted ${sampleUsers.length} users`);

    // Create a mapping for easier reference
    const userMap = {
      'user-1': sampleUsers[0].id, // Sophia
      'user-2': sampleUsers[1].id, // Alex
      'user-3': sampleUsers[2].id, // Sarah
      'user-4': sampleUsers[3].id, // David
      'user-5': sampleUsers[4].id, // Ethan
    };

    // Insert user profiles
    const profiles = await db.insert(userProfiles).values([
      {
        userId: sampleUsers[0].id,
        bio: 'Food enthusiast and team lead',
        location: 'San Francisco, CA',
        department: 'Engineering',
        joinedYear: 2021,
        dietaryPreferences: JSON.stringify(['Vegetarian', 'Gluten-free']),
        favoriteRestaurants: JSON.stringify(['Green Garden', 'Pasta Palace']),
      },
      {
        userId: sampleUsers[1].id,
        bio: 'Always up for trying new cuisines',
        location: 'New York, NY',
        department: 'Product',
        joinedYear: 2022,
        dietaryPreferences: JSON.stringify(['No restrictions']),
        favoriteRestaurants: JSON.stringify(['Taco Bell', 'Burger King']),
      },
      {
        userId: sampleUsers[2].id,
        bio: 'Sushi lover and health conscious',
        location: 'Seattle, WA',
        department: 'Design',
        joinedYear: 2020,
        dietaryPreferences: JSON.stringify(['Pescatarian', 'Low-carb']),
        favoriteRestaurants: JSON.stringify(['Sushi Zen', 'Fresh Market']),
      },
    ]).returning();

    console.log(`✅ Inserted ${profiles.length} user profiles`);

    // Insert user stats
    const stats = await db.insert(userStats).values([
      {
        userId: sampleUsers[0].id,
        totalVotes: 120,
        totalSuggestions: 15,
        totalWins: 8,
        totalTopPicks: 20,
        averageRating: '4.2',
      },
      {
        userId: sampleUsers[1].id,
        totalVotes: 85,
        totalSuggestions: 12,
        totalWins: 5,
        totalTopPicks: 15,
        averageRating: '3.8',
      },
      {
        userId: sampleUsers[2].id,
        totalVotes: 95,
        totalSuggestions: 18,
        totalWins: 7,
        totalTopPicks: 22,
        averageRating: '4.1',
      },
    ]).returning();

    console.log(`✅ Inserted ${stats.length} user stats`);

    // Insert achievements
    const achievementData = await db.insert(achievements).values([
      {
        title: 'Suggestion Superstar',
        description: 'Your suggestions have been top-rated 5 times!',
        iconName: 'Trophy',
        category: 'suggestions',
        requirement: 5,
      },
      {
        title: 'Voting Virtuoso',
        description: 'You\'ve voted in 100+ polls!',
        iconName: 'Star',
        category: 'voting',
        requirement: 100,
      },
      {
        title: 'Foodie Firestarter',
        description: 'You\'ve submitted 50+ suggestions!',
        iconName: 'Flame',
        category: 'suggestions',
        requirement: 50,
      },
      {
        title: 'Decision Dynamo',
        description: 'You\'ve participated in 20+ polls!',
        iconName: 'CheckCircle',
        category: 'voting',
        requirement: 20,
      },
    ]).returning();

    console.log(`✅ Inserted ${achievementData.length} achievements`);

    // Insert sample food suggestions
    const suggestions = await db.insert(foodSuggestions).values([
      {
        name: 'Tacos',
        description: 'Delicious tacos with a variety of fillings',
        imageUrl: '/placeholder.svg?height=200&width=400',
        userId: sampleUsers[0].id,
      },
      {
        name: 'Sushi',
        description: 'Fresh sushi rolls and sashimi',
        imageUrl: '/placeholder.svg?height=200&width=400',
        userId: sampleUsers[1].id,
      },
      {
        name: 'Pizza',
        description: 'Classic pizza with your favorite toppings',
        imageUrl: '/placeholder.svg?height=200&width=400',
        userId: sampleUsers[2].id,
      },
      {
        name: 'Burger',
        description: 'Juicy beef burger with all the fixings',
        imageUrl: '/placeholder.svg?height=200&width=400',
        userId: sampleUsers[0].id,
      },
      {
        name: 'Pasta',
        description: 'Creamy pasta with fresh herbs',
        imageUrl: '/placeholder.svg?height=200&width=400',
        userId: sampleUsers[1].id,
      },
    ]).returning();

    console.log(`✅ Inserted ${suggestions.length} food suggestions`);

    // Insert sample ratings
    const sampleRatings = [];
    for (const suggestion of suggestions) {
      // Add some random votes for each suggestion
      const numVotes = Math.floor(Math.random() * 10) + 1;
      for (let i = 0; i < numVotes; i++) {
        const isLike = Math.random() > 0.3; // 70% chance of like
        const randomUser = sampleUsers[Math.floor(Math.random() * sampleUsers.length)];
        sampleRatings.push({
          foodSuggestionId: suggestion.id,
          userId: randomUser.id,
          likes: isLike ? 1 : 0,
          dislikes: isLike ? 0 : 1,
        });
      }
    }

    if (sampleRatings.length > 0) {
      await db.insert(ratings).values(sampleRatings);
      console.log(`✅ Inserted ${sampleRatings.length} ratings`);
    }

    // Insert voting history
    const votingHistoryData = [];
    for (const suggestion of suggestions) {
      for (const user of sampleUsers) {
        if (Math.random() > 0.3) { // 70% chance of voting
          const voteType = Math.random() > 0.3 ? 'like' : 'dislike';
          votingHistoryData.push({
            userId: user.id,
            foodSuggestionId: suggestion.id,
            voteType,
          });
        }
      }
    }

    if (votingHistoryData.length > 0) {
      await db.insert(votingHistory).values(votingHistoryData);
      console.log(`✅ Inserted ${votingHistoryData.length} voting history records`);
    }

    // Insert user achievements (unlock some achievements for users)
    const userAchievementData = [];
    for (let i = 0; i < Math.min(3, sampleUsers.length); i++) {
      for (let j = 0; j < Math.min(2, achievementData.length); j++) {
        userAchievementData.push({
          userId: sampleUsers[i].id,
          achievementId: achievementData[j].id,
          progress: achievementData[j].requirement,
        });
      }
    }

    if (userAchievementData.length > 0) {
      await db.insert(userAchievements).values(userAchievementData);
      console.log(`✅ Inserted ${userAchievementData.length} user achievements`);
    }

    // Insert compatibility data
    const compatibilityData = [];
    for (let i = 0; i < sampleUsers.length; i++) {
      for (let j = i + 1; j < sampleUsers.length; j++) {
        const score = Math.random() * 100;
        const sharedVotes = Math.floor(Math.random() * 20) + 1;
        compatibilityData.push({
          userId1: sampleUsers[i].id,
          userId2: sampleUsers[j].id,
          compatibilityScore: score.toFixed(2),
          sharedVotes,
          totalComparisons: sharedVotes + Math.floor(Math.random() * 10),
        });
      }
    }

    if (compatibilityData.length > 0) {
      await db.insert(userCompatibility).values(compatibilityData);
      console.log(`✅ Inserted ${compatibilityData.length} compatibility records`);
    }

    console.log('🎉 Database seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seed function
seed().then(() => {
  console.log('✨ Seeding completed');
  process.exit(0);
});

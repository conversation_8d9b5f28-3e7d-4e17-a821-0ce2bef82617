import { <PERSON><PERSON>ef<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BottomNav } from "@/components/bottom-nav"
import Link from "next/link"

export default function HistoryPage() {
  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 pt-12">
        <Link href="/">
          <Button variant="ghost" size="icon" className="text-white hover:bg-zinc-800">
            <ArrowLeft className="h-6 w-6" />
          </Button>
        </Link>
        <h1 className="text-xl font-semibold">History</h1>
        <div className="w-10" />
      </div>

      <div className="px-4 pb-24">
        <div className="flex items-center justify-center h-96">
          <p className="text-zinc-400">History page coming soon...</p>
        </div>
      </div>

      <BottomNav currentPage="history" />
    </div>
  )
}

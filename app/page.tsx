"use client"

import Image from "next/image"
import { Plus, ThumbsUp, ThumbsDown, Edit, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { BottomNav } from "@/components/bottom-nav"
import { SuggestMealModal } from "@/components/suggest-meal-modal"
import { EditMealModal } from "@/components/edit-meal-modal"
import { Loading, LoadingCard } from "@/components/ui/loading"
import { ErrorBoundary } from "@/components/ui/error-boundary"
import { useState, useEffect } from "react"
import { apiRequest, getErrorMessage } from "@/lib/api-utils"
import type { ApiResponse, FoodSuggestionWithRating, FoodSuggestion } from "@/lib/types"

// Hardcoded user ID for demo purposes (using real user from seeded data)
const CURRENT_USER_ID = "64c3ab49-7c5d-4f14-b06e-1c5c8e9993ed" // Sophia

export default function HomePage() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingSuggestion, setEditingSuggestion] = useState<FoodSuggestion | null>(null)
  const [foodSuggestions, setFoodSuggestions] = useState<FoodSuggestionWithRating[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch food suggestions from API
  const fetchFoodSuggestions = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const result = await apiRequest<FoodSuggestionWithRating[]>('/api/food-suggestions')

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch suggestions')
      }

      setFoodSuggestions(result.data || [])
    } catch (error) {
      console.error('Error fetching food suggestions:', error)
      setError(getErrorMessage(error instanceof Error ? error : 'Failed to load suggestions'))
    } finally {
      setIsLoading(false)
    }
  }

  // Load suggestions on component mount
  useEffect(() => {
    fetchFoodSuggestions()
  }, [])

  // Handle adding a new suggestion
  const handleSuggestionAdded = (newSuggestion: FoodSuggestion) => {
    // Add the new suggestion to the list with default rating values
    const suggestionWithRating: FoodSuggestionWithRating = {
      ...newSuggestion,
      totalLikes: 0,
      totalDislikes: 0,
      userVote: null,
    }
    setFoodSuggestions(prev => [suggestionWithRating, ...prev])
  }

  const handleVote = async (suggestionId: string, voteType: "like" | "dislike") => {
    try {
      const response = await fetch(`/api/food-suggestions/${suggestionId}/vote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: CURRENT_USER_ID,
          voteType,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to vote')
      }

      // Refresh the suggestions to get updated vote counts
      await fetchFoodSuggestions()
    } catch (error) {
      console.error('Error voting:', error)
    }
  }

  const handleEdit = (suggestion: FoodSuggestionWithRating) => {
    setEditingSuggestion(suggestion)
    setIsEditModalOpen(true)
  }

  const handleSuggestionUpdated = (updatedSuggestion: FoodSuggestion) => {
    setFoodSuggestions(prev =>
      prev.map(suggestion =>
        suggestion.id === updatedSuggestion.id
          ? { ...suggestion, ...updatedSuggestion }
          : suggestion
      )
    )
    setIsEditModalOpen(false)
    setEditingSuggestion(null)
  }

  const handleDelete = async (suggestionId: string) => {
    if (!confirm('Are you sure you want to delete this suggestion?')) {
      return
    }

    try {
      const response = await fetch(`/api/food-suggestions/${suggestionId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete suggestion')
      }

      // Remove the suggestion from the list
      setFoodSuggestions(prev => prev.filter(suggestion => suggestion.id !== suggestionId))
    } catch (error) {
      console.error('Error deleting suggestion:', error)
      alert('Failed to delete suggestion')
    }
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-zinc-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 pt-12">
        <div className="w-10" />
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-sm">MM</span>
          </div>
          <span className="text-lg font-semibold">Meal Match</span>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="text-white hover:bg-zinc-800"
          onClick={() => setIsModalOpen(true)}
        >
          <Plus className="h-6 w-6" />
        </Button>
      </div>

      {/* Food Options */}
      <div className="px-4 pb-24 space-y-6">
        {isLoading ? (
          <div className="space-y-4">
            <LoadingCard />
            <LoadingCard />
            <LoadingCard />
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-red-400 mb-2">{error}</p>
              <Button
                onClick={fetchFoodSuggestions}
                variant="outline"
                className="text-white border-zinc-700 hover:bg-zinc-800"
              >
                Try Again
              </Button>
            </div>
          </div>
        ) : foodSuggestions.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-zinc-400 mb-4">No suggestions yet. Be the first to suggest a meal!</p>
              <Button
                onClick={() => setIsModalOpen(true)}
                className="bg-pink-500 hover:bg-pink-600 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Suggest a Meal
              </Button>
            </div>
          </div>
        ) : (
          foodSuggestions.map((suggestion) => (
            <Card key={suggestion.id} className="bg-zinc-800 border-zinc-700 overflow-hidden">
              <CardContent className="p-0">
                <div>
                  {/* Left side - Image and content */}
                  <div className="flex-1">
                    {/* Food Image */}
                    <div className="bg-zinc-700 overflow-hidden">
                      <Image
                        src={suggestion.imageUrl || "/placeholder.svg?height=200&width=400"}
                        alt={suggestion.name}
                        width={400}
                        height={200}
                        className="w-full h-48 object-cover"
                      />
                    </div>

                    {/* Food Details */}
                    <div className="p-4 space-y-2">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h2 className="text-xl font-semibold text-white">{suggestion.name}</h2>
                          {suggestion.description && (
                            <p className="text-zinc-400 text-sm mt-1">{suggestion.description}</p>
                          )}
                          <p className="text-zinc-500 text-sm mt-1">Submitted by User {suggestion.userId}</p>
                        </div>

                        {/* Edit/Delete buttons - only show for current user's suggestions */}
                        {suggestion.userId === CURRENT_USER_ID && (
                          <div className="flex items-center space-x-1 ml-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-zinc-400 hover:bg-zinc-700 hover:text-blue-400"
                              onClick={() => handleEdit(suggestion)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-zinc-400 hover:bg-zinc-700 hover:text-red-400"
                              onClick={() => handleDelete(suggestion.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>

                      {/* Like/Dislike buttons */}
                      <div className="flex items-center space-x-4 pt-2">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className={`h-8 px-3 rounded-full ${
                              suggestion.userVote === "like"
                                ? "bg-green-600 text-white hover:bg-green-700"
                                : "text-zinc-400 hover:bg-zinc-700 hover:text-green-400"
                            }`}
                            onClick={() => handleVote(suggestion.id, "like")}
                          >
                            <ThumbsUp className="h-4 w-4 mr-1" />
                            <span className="text-sm">{suggestion.totalLikes}</span>
                          </Button>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className={`h-8 px-3 rounded-full ${
                              suggestion.userVote === "dislike"
                                ? "bg-red-600 text-white hover:bg-red-700"
                                : "text-zinc-400 hover:bg-zinc-700 hover:text-red-400"
                            }`}
                            onClick={() => handleVote(suggestion.id, "dislike")}
                          >
                            <ThumbsDown className="h-4 w-4 mr-1" />
                            <span className="text-sm">{suggestion.totalDislikes}</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <SuggestMealModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuggestionAdded={handleSuggestionAdded}
      />

      <EditMealModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setEditingSuggestion(null)
        }}
        suggestion={editingSuggestion}
        onSuggestionUpdated={handleSuggestionUpdated}
      />

        <BottomNav currentPage="home" />
      </div>
    </ErrorBoundary>
  )
}

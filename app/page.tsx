"use client"

import Image from "next/image"
import { Plus, ThumbsUp, ThumbsDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { BottomNav } from "@/components/bottom-nav"
import { SuggestMealModal } from "@/components/suggest-meal-modal"
import { useState } from "react"

const lunchOptions = [
  {
    id: 1,
    title: "Tacos",
    description: "Delicious tacos with a variety of fillings",
    submittedBy: "<PERSON>",
    image: "/placeholder.svg?height=200&width=400",
    bgColor: "bg-blue-100",
    likes: 24,
    dislikes: 3,
  },
  {
    id: 2,
    title: "Sushi",
    description: "Fresh sushi rolls and sashimi",
    submittedBy: "<PERSON>",
    image: "/placeholder.svg?height=200&width=400",
    bgColor: "bg-orange-200",
    likes: 18,
    dislikes: 2,
  },
  {
    id: 3,
    title: "Pizza",
    description: "Classic pizza with your favorite toppings",
    submittedBy: "<PERSON>",
    image: "/placeholder.svg?height=200&width=400",
    bgColor: "bg-yellow-100",
    likes: 31,
    dislikes: 5,
  },
]

export default function HomePage() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [optionLikes, setOptionLikes] = useState(
    lunchOptions.reduce(
      (acc, option) => {
        acc[option.id] = { likes: option.likes, dislikes: option.dislikes, userVote: null }
        return acc
      },
      {} as Record<number, { likes: number; dislikes: number; userVote: "like" | "dislike" | null }>,
    ),
  )

  const handleVote = (optionId: number, voteType: "like" | "dislike") => {
    setOptionLikes((prev) => {
      const current = prev[optionId]
      const newVote = current.userVote === voteType ? null : voteType

      let newLikes = current.likes
      let newDislikes = current.dislikes

      // Remove previous vote
      if (current.userVote === "like") newLikes--
      if (current.userVote === "dislike") newDislikes--

      // Add new vote
      if (newVote === "like") newLikes++
      if (newVote === "dislike") newDislikes++

      return {
        ...prev,
        [optionId]: {
          likes: newLikes,
          dislikes: newDislikes,
          userVote: newVote,
        },
      }
    })
  }

  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 pt-12">
        <div className="w-10" />
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-sm">L</span>
          </div>
          <span className="text-lg font-semibold">LunchVote</span>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="text-white hover:bg-zinc-800"
          onClick={() => setIsModalOpen(true)}
        >
          <Plus className="h-6 w-6" />
        </Button>
      </div>

      {/* Food Options */}
      <div className="px-4 pb-24 space-y-6">
        {lunchOptions.map((option) => {
          const currentVotes = optionLikes[option.id]
          return (
            <Card key={option.id} className="bg-zinc-800 border-zinc-700 overflow-hidden">
              <CardContent className="p-0">
                <div>
                  {/* Left side - Image and content */}
                  <div className="flex-1">
                    {/* Food Image */}
                    <div className={`${option.bgColor} overflow-hidden`}>
                      <Image
                        src={option.image || "/placeholder.svg"}
                        alt={option.title}
                        width={400}
                        height={200}
                        className="w-full h-48 object-cover"
                      />
                    </div>

                    {/* Food Details */}
                    <div className="p-4 space-y-2">
                      <h2 className="text-xl font-semibold text-white">{option.title}</h2>
                      <p className="text-zinc-400 text-sm">{option.description}</p>
                      <p className="text-zinc-500 text-sm">Submitted by {option.submittedBy}</p>

                      {/* Like/Dislike buttons */}
                      <div className="flex items-center space-x-4 pt-2">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className={`h-8 px-3 rounded-full ${
                              currentVotes.userVote === "like"
                                ? "bg-green-600 text-white hover:bg-green-700"
                                : "text-zinc-400 hover:bg-zinc-700 hover:text-green-400"
                            }`}
                            onClick={() => handleVote(option.id, "like")}
                          >
                            <ThumbsUp className="h-4 w-4 mr-1" />
                            <span className="text-sm">{currentVotes.likes}</span>
                          </Button>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className={`h-8 px-3 rounded-full ${
                              currentVotes.userVote === "dislike"
                                ? "bg-red-600 text-white hover:bg-red-700"
                                : "text-zinc-400 hover:bg-zinc-700 hover:text-red-400"
                            }`}
                            onClick={() => handleVote(option.id, "dislike")}
                          >
                            <ThumbsDown className="h-4 w-4 mr-1" />
                            <span className="text-sm">{currentVotes.dislikes}</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <SuggestMealModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />

      <BottomNav currentPage="home" />
    </div>
  )
}

import { <PERSON>L<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, CheckCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { BottomNav } from "@/components/bottom-nav"
import Link from "next/link"

const achievements = [
  {
    id: 1,
    title: "Suggestion Superstar",
    description: "Your suggestions have been top-rated 5 times!",
    icon: Trophy,
    completed: true,
  },
  {
    id: 2,
    title: "Voting Virtuoso",
    description: "You've voted in 100+ polls!",
    icon: Star,
    completed: true,
  },
  {
    id: 3,
    title: "Foodie Firestarter",
    description: "You've submitted 50+ suggestions!",
    icon: Flame,
    completed: true,
  },
  {
    id: 4,
    title: "Decision Dynamo",
    description: "You've participated in 20+ polls!",
    icon: CheckCircle,
    completed: true,
  },
]

const upcomingAchievements = [
  {
    id: 1,
    title: "Suggestion Superstar",
    description: "Get your suggestions top-rated 10 times!",
    icon: Trophy,
    completed: false,
  },
  {
    id: 2,
    title: "Voting Virtuoso",
    description: "Vote in 200+ polls!",
    icon: Star,
    completed: false,
  },
  {
    id: 3,
    title: "Foodie Firestarter",
    description: "Submit 100+ suggestions!",
    icon: Flame,
    completed: false,
  },
  {
    id: 4,
    title: "Decision Dynamo",
    description: "Participate in 50+ polls!",
    icon: CheckCircle,
    completed: false,
  },
]

export default function AchievementsPage() {
  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 pt-12">
        <Link href="/">
          <Button variant="ghost" size="icon" className="text-white hover:bg-zinc-800">
            <ArrowLeft className="h-6 w-6" />
          </Button>
        </Link>
        <h1 className="text-xl font-semibold">Achievements</h1>
        <div className="w-10" />
      </div>

      <div className="px-4 pb-24 space-y-8">
        {/* Your Achievements */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold">Your Achievements</h2>
          <div className="grid grid-cols-2 gap-4">
            {achievements.map((achievement) => {
              const IconComponent = achievement.icon
              return (
                <Card key={achievement.id} className="bg-zinc-800 border-zinc-700">
                  <CardContent className="p-4 space-y-3">
                    <IconComponent className="h-8 w-8 text-white" />
                    <div>
                      <h3 className="font-semibold text-white">{achievement.title}</h3>
                      <p className="text-zinc-400 text-sm">{achievement.description}</p>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Upcoming Achievements */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold">Upcoming Achievements</h2>
          <div className="grid grid-cols-2 gap-4">
            {upcomingAchievements.map((achievement) => {
              const IconComponent = achievement.icon
              return (
                <Card key={achievement.id} className="bg-zinc-800 border-zinc-700 opacity-60">
                  <CardContent className="p-4 space-y-3">
                    <IconComponent className="h-8 w-8 text-zinc-400" />
                    <div>
                      <h3 className="font-semibold text-zinc-300">{achievement.title}</h3>
                      <p className="text-zinc-500 text-sm">{achievement.description}</p>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </div>

      <BottomNav currentPage="achievements" />
    </div>
  )
}

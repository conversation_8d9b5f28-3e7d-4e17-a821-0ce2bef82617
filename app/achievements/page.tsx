"use client"

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Star, Flame, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { BottomNav } from "@/components/bottom-nav"
import Link from "next/link"
import { useState, useEffect } from "react"
import { Loading } from "@/components/ui/loading"
import { ErrorBoundary } from "@/components/ui/error-boundary"
import { apiRequest, getErrorMessage } from "@/lib/api-utils"
import type { ApiResponse, AchievementWithProgress } from "@/lib/types"

// Hardcoded user ID for demo purposes (using real user from seeded data)
const CURRENT_USER_ID = "64c3ab49-7c5d-4f14-b06e-1c5c8e9993ed" // Sophia

// Icon mapping for achievements
const iconMap = {
  Trophy,
  Star,
  Flame,
  CheckCircle,
}

export default function AchievementsPage() {
  const [achievements, setAchievements] = useState<AchievementWithProgress[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch achievements data
  const fetchAchievements = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/users/${CURRENT_USER_ID}/achievements`)
      const result: ApiResponse<AchievementWithProgress[]> = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch achievements')
      }

      setAchievements(result.data || [])
    } catch (error) {
      console.error('Error fetching achievements:', error)
      setError(error instanceof Error ? error.message : 'Failed to load achievements')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchAchievements()
  }, [])

  // Separate unlocked and locked achievements
  const unlockedAchievements = achievements.filter(a => a.isUnlocked)
  const lockedAchievements = achievements.filter(a => !a.isUnlocked)

  if (isLoading) {
    return (
      <div className="min-h-screen bg-zinc-900 text-white flex items-center justify-center">
        <Loading size="lg" text="Loading achievements..." />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-zinc-900 text-white flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 mb-2">{error}</p>
          <Button onClick={fetchAchievements} variant="outline" className="text-white border-zinc-700">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-zinc-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 pt-12">
        <Link href="/">
          <Button variant="ghost" size="icon" className="text-white hover:bg-zinc-800">
            <ArrowLeft className="h-6 w-6" />
          </Button>
        </Link>
        <h1 className="text-xl font-semibold">Achievements</h1>
        <div className="w-10" />
      </div>

      <div className="px-4 pb-24 space-y-8">
        {/* Your Achievements */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold">Your Achievements</h2>
          {unlockedAchievements.length === 0 ? (
            <p className="text-zinc-400 text-center py-8">No achievements unlocked yet. Keep participating to earn your first achievement!</p>
          ) : (
            <div className="grid grid-cols-2 gap-4">
              {unlockedAchievements.map((achievement) => {
                const IconComponent = iconMap[achievement.iconName as keyof typeof iconMap] || Trophy
                return (
                  <Card key={achievement.id} className="bg-zinc-800 border-zinc-700">
                    <CardContent className="p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <IconComponent className="h-8 w-8 text-yellow-500" />
                        <span className="text-xs text-green-400 font-medium">UNLOCKED</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-white">{achievement.title}</h3>
                        <p className="text-zinc-400 text-sm">{achievement.description}</p>
                        {achievement.unlockedAt && (
                          <p className="text-zinc-500 text-xs mt-1">
                            Unlocked {new Date(achievement.unlockedAt).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>

        {/* Upcoming Achievements */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold">Upcoming Achievements</h2>
          {lockedAchievements.length === 0 ? (
            <p className="text-zinc-400 text-center py-8">All achievements unlocked! Great job! 🎉</p>
          ) : (
            <div className="grid grid-cols-2 gap-4">
              {lockedAchievements.map((achievement) => {
                const IconComponent = iconMap[achievement.iconName as keyof typeof iconMap] || Trophy
                const progressPercentage = (achievement.progress / achievement.requirement) * 100
                return (
                  <Card key={achievement.id} className="bg-zinc-800 border-zinc-700 opacity-60">
                    <CardContent className="p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <IconComponent className="h-8 w-8 text-zinc-400" />
                        <span className="text-xs text-zinc-500 font-medium">
                          {achievement.progress}/{achievement.requirement}
                        </span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-zinc-300">{achievement.title}</h3>
                        <p className="text-zinc-500 text-sm">{achievement.description}</p>

                        {/* Progress bar */}
                        <div className="mt-2">
                          <div className="w-full bg-zinc-700 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                            />
                          </div>
                          <p className="text-xs text-zinc-500 mt-1">
                            {progressPercentage.toFixed(0)}% complete
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>
      </div>

        <BottomNav currentPage="achievements" />
      </div>
    </ErrorBoundary>
  )
}

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { foodSuggestions, ratings } from '@/lib/schema';
import { eq, sql } from 'drizzle-orm';
import { updateUserStats } from '@/lib/stats-utils';
import type { ApiResponse, FoodSuggestionWithRating, FoodSuggestionFormData } from '@/lib/types';

// GET /api/food-suggestions - List all food suggestions with ratings
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    // Query food suggestions with aggregated ratings
    const result = await db
      .select({
        id: foodSuggestions.id,
        name: foodSuggestions.name,
        imageUrl: foodSuggestions.imageUrl,
        description: foodSuggestions.description,
        userId: foodSuggestions.userId,
        ratingId: foodSuggestions.ratingId,
        createdAt: foodSuggestions.createdAt,
        updatedAt: foodSuggestions.updatedAt,
        totalLikes: sql<number>`COALESCE(SUM(CASE WHEN ${ratings.likes} > 0 THEN ${ratings.likes} ELSE 0 END), 0)`,
        totalDislikes: sql<number>`COALESCE(SUM(CASE WHEN ${ratings.dislikes} > 0 THEN ${ratings.dislikes} ELSE 0 END), 0)`,
      })
      .from(foodSuggestions)
      .leftJoin(ratings, eq(foodSuggestions.id, ratings.foodSuggestionId))
      .groupBy(foodSuggestions.id)
      .orderBy(foodSuggestions.createdAt);

    const suggestions: FoodSuggestionWithRating[] = result.map(item => ({
      ...item,
      userVote: null, // TODO: Implement user-specific vote tracking
    }));

    const response: ApiResponse<FoodSuggestionWithRating[]> = {
      success: true,
      data: suggestions,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching food suggestions:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch food suggestions',
    };
    return NextResponse.json(response, { status: 500 });
  }
}

// POST /api/food-suggestions - Create a new food suggestion
export async function POST(request: NextRequest) {
  try {
    const body: FoodSuggestionFormData = await request.json();
    
    if (!body.name || !body.userId) {
      const response: ApiResponse<null> = {
        success: false,
        error: 'Name and userId are required',
      };
      return NextResponse.json(response, { status: 400 });
    }

    const [newSuggestion] = await db
      .insert(foodSuggestions)
      .values({
        name: body.name,
        imageUrl: body.imageUrl || null,
        description: body.description || null,
        userId: body.userId,
      })
      .returning();

    // Update user stats after creating suggestion
    await updateUserStats(body.userId);

    const response: ApiResponse<typeof newSuggestion> = {
      success: true,
      data: newSuggestion,
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error('Error creating food suggestion:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to create food suggestion',
    };
    return NextResponse.json(response, { status: 500 });
  }
}

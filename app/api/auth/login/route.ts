import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth';
import type { ApiResponse } from '@/lib/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password } = body;

    if (!username || !password) {
      const response: ApiResponse<null> = {
        success: false,
        error: 'Username and password are required',
      };
      return NextResponse.json(response, { status: 400 });
    }

    const user = await authenticateUser(username, password);

    if (!user) {
      const response: ApiResponse<null> = {
        success: false,
        error: 'Invalid username or password',
      };
      return NextResponse.json(response, { status: 401 });
    }

    const response: ApiResponse<typeof user> = {
      success: true,
      data: user,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Login error:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Login failed',
    };
    return NextResponse.json(response, { status: 500 });
  }
}

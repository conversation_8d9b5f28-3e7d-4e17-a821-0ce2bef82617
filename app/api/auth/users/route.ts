import { NextResponse } from 'next/server';
import { getAllUsers } from '@/lib/auth';
import type { ApiResponse } from '@/lib/types';

export async function GET() {
  try {
    const users = await getAllUsers();

    const response: ApiResponse<typeof users> = {
      success: true,
      data: users,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching users:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch users',
    };
    return NextResponse.json(response, { status: 500 });
  }
}

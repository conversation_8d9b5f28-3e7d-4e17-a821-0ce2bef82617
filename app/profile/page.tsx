import Image from "next/image"
import { ArrowLeft } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { BottomNav } from "@/components/bottom-nav"
import Link from "next/link"

const votingHistory = [
  {
    id: 1,
    name: "Pasta Paradise",
    cuisine: "Italian",
    image: "/placeholder.svg?height=60&width=60",
    bgColor: "bg-yellow-600",
  },
  {
    id: 2,
    name: "Taco Fiesta",
    cuisine: "Mexican",
    image: "/placeholder.svg?height=60&width=60",
    bgColor: "bg-green-600",
  },
  {
    id: 3,
    name: "Sushi Spot",
    cuisine: "Japanese",
    image: "/placeholder.svg?height=60&width=60",
    bgColor: "bg-gray-600",
  },
]

const colleagues = [
  {
    id: 1,
    name: "<PERSON>",
    match: "85% Match",
    avatar: "/placeholder.svg?height=50&width=50",
  },
  {
    id: 2,
    name: "<PERSON>",
    match: "70% Match",
    avatar: "/placeholder.svg?height=50&width=50",
  },
  {
    id: 3,
    name: "<PERSON>",
    match: "30% Match",
    avatar: "/placeholder.svg?height=50&width=50",
  },
]

export default function ProfilePage() {
  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 pt-12">
        <Link href="/">
          <Button variant="ghost" size="icon" className="text-white hover:bg-zinc-800">
            <ArrowLeft className="h-6 w-6" />
          </Button>
        </Link>
        <h1 className="text-xl font-semibold">Profile</h1>
        <div className="w-10" />
      </div>

      <div className="px-4 pb-24 space-y-6">
        {/* Profile Info */}
        <div className="flex flex-col items-center space-y-4">
          <div className="w-32 h-32 rounded-full bg-orange-200 overflow-hidden">
            <Image
              src="/placeholder.svg?height=128&width=128"
              alt="Sophia"
              width={128}
              height={128}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="text-center">
            <h2 className="text-2xl font-semibold">Sophia</h2>
            <p className="text-zinc-400">@sophia</p>
            <p className="text-zinc-500 text-sm">Joined 2021</p>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4">
          <Card className="bg-zinc-800 border-zinc-700">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">120</div>
              <div className="text-zinc-400 text-sm">Votes</div>
            </CardContent>
          </Card>
          <Card className="bg-zinc-800 border-zinc-700">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">80</div>
              <div className="text-zinc-400 text-sm">Wins</div>
            </CardContent>
          </Card>
          <Card className="bg-zinc-800 border-zinc-700">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">20</div>
              <div className="text-zinc-400 text-sm">Top Picks</div>
            </CardContent>
          </Card>
        </div>

        {/* Voting History */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold">Voting History</h3>
            <Button variant="ghost" className="text-zinc-400 text-sm hover:bg-zinc-800">
              See All
            </Button>
          </div>
          <div className="space-y-3">
            {votingHistory.map((item) => (
              <div key={item.id} className="flex items-center space-x-3">
                <div className={`w-12 h-12 rounded-lg ${item.bgColor} overflow-hidden`}>
                  <Image
                    src={item.image || "/placeholder.svg"}
                    alt={item.name}
                    width={48}
                    height={48}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <div className="font-medium">{item.name}</div>
                  <div className="text-zinc-400 text-sm">{item.cuisine}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Colleague Compatibility */}
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Colleague Compatibility</h3>
          <div className="space-y-3">
            {colleagues.map((colleague) => (
              <div key={colleague.id} className="flex items-center space-x-3">
                <div className="w-12 h-12 rounded-full overflow-hidden">
                  <Image
                    src={colleague.avatar || "/placeholder.svg"}
                    alt={colleague.name}
                    width={48}
                    height={48}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <div className="font-medium">{colleague.name}</div>
                  <div className="text-zinc-400 text-sm">{colleague.match}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <BottomNav currentPage="profile" />
    </div>
  )
}

"use client"

import Image from "next/image"
import { <PERSON><PERSON>ef<PERSON>, Edit } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { BottomNav } from "@/components/bottom-nav"
import { EditProfileModal } from "@/components/edit-profile-modal"
import Link from "next/link"
import { useState, useEffect } from "react"
import type {
  ApiResponse,
  UserWithProfile,
  UserStatsData,
  VotingHistoryItem,
  CompatibilityData
} from "@/lib/types"

// Hardcoded user ID for demo purposes
const CURRENT_USER_ID = "user-1"

export default function ProfilePage() {
  const [user, setUser] = useState<UserWithProfile | null>(null)
  const [stats, setStats] = useState<UserStatsData | null>(null)
  const [votingHistory, setVotingHistory] = useState<VotingHistoryItem[]>([])
  const [compatibility, setCompatibility] = useState<CompatibilityData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)

  // Fetch user data
  const fetchUserData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Fetch user profile
      const userResponse = await fetch(`/api/users/${CURRENT_USER_ID}`)
      const userResult: ApiResponse<UserWithProfile> = await userResponse.json()

      if (!userResult.success) {
        throw new Error(userResult.error || 'Failed to fetch user')
      }

      setUser(userResult.data || null)

      // Fetch user stats
      const statsResponse = await fetch(`/api/users/${CURRENT_USER_ID}/stats`)
      const statsResult: ApiResponse<UserStatsData> = await statsResponse.json()

      if (statsResult.success) {
        setStats(statsResult.data || null)
      }

      // Fetch voting history (limit to 5 recent items)
      const historyResponse = await fetch(`/api/users/${CURRENT_USER_ID}/voting-history?limit=5`)
      const historyResult: ApiResponse<VotingHistoryItem[]> = await historyResponse.json()

      if (historyResult.success) {
        setVotingHistory(historyResult.data || [])
      }

      // Fetch compatibility data
      const compatibilityResponse = await fetch(`/api/users/${CURRENT_USER_ID}/compatibility`)
      const compatibilityResult: ApiResponse<CompatibilityData[]> = await compatibilityResponse.json()

      if (compatibilityResult.success) {
        setCompatibility(compatibilityResult.data || [])
      }

    } catch (error) {
      console.error('Error fetching user data:', error)
      setError(error instanceof Error ? error.message : 'Failed to load profile')
    } finally {
      setIsLoading(false)
    }
  }

  const handleProfileUpdated = (updatedUser: UserWithProfile) => {
    setUser(updatedUser)
    setIsEditModalOpen(false)
  }

  useEffect(() => {
    fetchUserData()
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-zinc-900 text-white flex items-center justify-center">
        <p className="text-zinc-400">Loading profile...</p>
      </div>
    )
  }

  if (error || !user) {
    return (
      <div className="min-h-screen bg-zinc-900 text-white flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 mb-2">{error || 'User not found'}</p>
          <Button onClick={fetchUserData} variant="outline" className="text-white border-zinc-700">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 pt-12">
        <Link href="/">
          <Button variant="ghost" size="icon" className="text-white hover:bg-zinc-800">
            <ArrowLeft className="h-6 w-6" />
          </Button>
        </Link>
        <h1 className="text-xl font-semibold">Profile</h1>
        <div className="w-10" />
      </div>

      <div className="px-4 pb-24 space-y-6">
        {/* Profile Info */}
        <div className="flex flex-col items-center space-y-4">
          <div className="relative">
            <div className="w-32 h-32 rounded-full bg-orange-200 overflow-hidden">
              <Image
                src={user.avatarUrl || "/placeholder.svg?height=128&width=128"}
                alt={user.displayName}
                width={128}
                height={128}
                className="w-full h-full object-cover"
              />
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="absolute bottom-0 right-0 w-8 h-8 bg-zinc-800 hover:bg-zinc-700 rounded-full"
              onClick={() => setIsEditModalOpen(true)}
            >
              <Edit className="h-4 w-4" />
            </Button>
          </div>
          <div className="text-center">
            <h2 className="text-2xl font-semibold">{user.displayName}</h2>
            <p className="text-zinc-400">@{user.username}</p>
            {user.profile?.joinedYear && (
              <p className="text-zinc-500 text-sm">Joined {user.profile.joinedYear}</p>
            )}
            {user.profile?.department && (
              <p className="text-zinc-500 text-sm">{user.profile.department}</p>
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4">
          <Card className="bg-zinc-800 border-zinc-700">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">{stats?.totalVotes || 0}</div>
              <div className="text-zinc-400 text-sm">Votes</div>
            </CardContent>
          </Card>
          <Card className="bg-zinc-800 border-zinc-700">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">{stats?.totalWins || 0}</div>
              <div className="text-zinc-400 text-sm">Wins</div>
            </CardContent>
          </Card>
          <Card className="bg-zinc-800 border-zinc-700">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">{stats?.totalTopPicks || 0}</div>
              <div className="text-zinc-400 text-sm">Top Picks</div>
            </CardContent>
          </Card>
        </div>

        {/* Voting History */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold">Voting History</h3>
            <Button variant="ghost" className="text-zinc-400 text-sm hover:bg-zinc-800">
              See All
            </Button>
          </div>
          <div className="space-y-3">
            {votingHistory.length === 0 ? (
              <p className="text-zinc-400 text-center py-4">No voting history yet</p>
            ) : (
              votingHistory.map((item) => (
                <div key={item.id} className="flex items-center space-x-3">
                  <div className="w-12 h-12 rounded-lg bg-zinc-700 overflow-hidden">
                    <Image
                      src={item.foodSuggestionImage || "/placeholder.svg?height=48&width=48"}
                      alt={item.foodSuggestionName}
                      width={48}
                      height={48}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{item.foodSuggestionName}</div>
                    <div className="text-zinc-400 text-sm">
                      {item.voteType === 'like' ? '👍 Liked' : '👎 Disliked'} • {item.cuisine}
                    </div>
                  </div>
                  <div className="text-zinc-500 text-xs">
                    {new Date(item.votedAt).toLocaleDateString()}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Colleague Compatibility */}
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Colleague Compatibility</h3>
          <div className="space-y-3">
            {compatibility.length === 0 ? (
              <p className="text-zinc-400 text-center py-4">No compatibility data yet</p>
            ) : (
              compatibility.slice(0, 5).map((colleague) => (
                <div key={colleague.userId} className="flex items-center space-x-3">
                  <div className="w-12 h-12 rounded-full overflow-hidden bg-zinc-700">
                    <Image
                      src={colleague.avatarUrl || "/placeholder.svg?height=48&width=48"}
                      alt={colleague.displayName}
                      width={48}
                      height={48}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{colleague.displayName}</div>
                    <div className="text-zinc-400 text-sm">
                      {colleague.compatibilityScore.toFixed(0)}% Match • {colleague.sharedVotes} shared votes
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      <EditProfileModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        user={user}
        onProfileUpdated={handleProfileUpdated}
      />

      <BottomNav currentPage="profile" />
    </div>
  )
}
